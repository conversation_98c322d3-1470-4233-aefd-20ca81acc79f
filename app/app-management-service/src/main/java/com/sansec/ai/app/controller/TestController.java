package com.sansec.ai.app.controller;

import com.sansec.common.param.response.SecRestResponse;
import com.sansec.common.utils.ResultUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/test")
@Validated
public class TestController {

    @PostMapping("/helloworld")
    public SecRestResponse<Object> helloworld() {
        return ResultUtil.ok("hello");
    }

}
