<?xml version="1.0" encoding="UTF-8"?>
<assembly
        xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.0
        http://maven.apache.org/xsd/assembly-1.1.0.xsd">
    <id>release</id>
    <formats>
        <format>tar.gz</format>
        <format>dir</format>
    </formats>
    <fileSets>
        <fileSet>
            <directory>target/</directory>
            <!-- 过滤 -->
            <includes>
                <include>crypto-ai-one-system.jar</include>
            </includes>
            <outputDirectory>/bin</outputDirectory>
        </fileSet>

        <fileSet>
            <directory>./scripts</directory>
            <outputDirectory>/scripts</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>./config</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>*</include>
            </includes>
        </fileSet>

        <fileSet>
            <directory>./src/main/resources/</directory>
            <outputDirectory>/config</outputDirectory>
            <includes>
                <include>/**</include>
            </includes>
        </fileSet>

    </fileSets>

</assembly>
