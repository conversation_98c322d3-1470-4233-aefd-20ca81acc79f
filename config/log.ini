[]
# 如果logback启用MDC，则在此处配置traceId值，多个逗号分隔，如果没有可不填
traceId = traceId
enableStorage = true
# Root日志级别，即全局日志级别设置 ---  如果需要打印开源jar包的日志可以修改该配置为DEBUG或TRACE
rootLevel = INFO
encoderPattern = %d{yyyy-MM-dd HH:mm:ss.SSS} {} [%thread] %-5level %logger{50} - %msg%n
# 当前项目包路径下的日志级别设置
level = DEBUG
# 当前项目包路径
rootPackage = com.sansec.ai
maxFileCount = 12
maxHistory = 7
# 服务名称 --- 与日志文件名称相关
serverName = crypto-ai-one-system

# Appender列表，可以设置多个。
appenderPatternList = ERROR,TRACE
configPath = ./log.ini
maxFileSize = 128MB
# 日志文件存放路径 --- 支持相对路径或绝对路径
logHome = logs