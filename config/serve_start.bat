@echo off

set "SCRIPT_PATH=%~f0"
for %%I in ("%SCRIPT_PATH%") do set "SCRIPT_DIR=%%~dpI"
docker inspect -f '{{.State.Status}}' secullama 2>nul >nul

if %errorlevel% equ 0 (
    docker start secullama
) else (
    docker run  --privileged -dit --env-file d:\var\ollama\env.list -v d:\var\ollama:/root/.ollama -v %SCRIPT_DIR%swsds.ini:/opt/secuLlama-hsm/swsds.ini -p 11434:11434 --name secullama secullama/secullama
)