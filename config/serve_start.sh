#!/bin/bash

SCRIPT_ATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )/" && pwd )"
# 检查Docker容器ollama是否存在
if docker inspect -f '{{.State.Status}}' secullama > /dev/null 2>&1; then
    # 容器存在，启动容器
    docker start secullama
else
    # 容器不存在，创建并运行容器
    docker run -dit --privileged --env-file /opt/sansec/secuLlama/env.list -v /opt/sansec/secuLlama:/root/.ollama -v $SCRIPT_ATH/swsds.ini:/opt/secuLlama-hsm/swsds.ini -p 11434:11434 --name secullama secullama/secullama
fi