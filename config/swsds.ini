#SWXA密码设备接口配置文件
[Device]
device=CMD
gm0018=0
[ErrorLog]
level=1
logfile=./SwxaLog/swsds.log
maxsize=100
count=3

[HSM1]
ip=************
port=8008
#passwd=11111111
passwd=801862E847C61584451AFDD1D9C1AAA1
[Timeout]
connect=5
service=5
heartbeat=10
[ConnectionPool]
poolsize=5
[SSL]
ssl=0
ssllog=1
jsseprovider=SwxaJSSE
jceprovider=CommonJCE
keymanagertype=X509
trustmanagertype=x509
protocol=TLCPv1.1
keystore=C:\Users\<USER>\Desktop\44.jks
keystoretype=J<PERSON>
keystorepassword=66666666
trustkeystore=C:\Users\<USER>\Desktop\44.jks
trustkeystoretype=J<PERSON>
trustkeystorepassword=66666666
#includeciphersuitefilter=*_RSA_*_SHA256$
#excludeciphersuitefilter=*_256_*
