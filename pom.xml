<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.sansec.ai</groupId>
    <artifactId>crypto-ai-one-system</artifactId>
    <version>1.0-SNAPSHOT</version>

    <parent>
        <groupId>com.sansec</groupId>
        <artifactId>sansec-parent</artifactId>
        <version>2.0.0-SNAPSHOT</version>
    </parent>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <out.jarname>crypto-ai-one-system</out.jarname>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.alibaba.cloud.ai</groupId>
            <artifactId>spring-ai-alibaba-graph-core</artifactId>
            <version>1.0.0.3</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.cloud.ai</groupId>-->
<!--            <artifactId>spring-ai-alibaba-starter-dashscope</artifactId>-->
<!--            <version>1.0.0.3</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-model-openai</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-advisors-vector-store</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-starter-vector-store-weaviate</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-weaviate-store</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-pdf-document-reader</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sansec.springboot</groupId>
            <artifactId>sansec-springboot-dependencies</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.sansec.log</groupId>
            <artifactId>sansec-log</artifactId>
        </dependency>

        <!--    sansec jce        -->
        <dependency>
            <groupId>com.sansec.jce</groupId>
            <artifactId>swxajce-v5</artifactId>
        </dependency>
        <!--    sansec jce        -->
        <dependency>
            <groupId>com.sansec.jce</groupId>
            <artifactId>crypto-v5</artifactId>
            <version>5.3.3.22</version>
        </dependency>

        <dependency>
            <groupId>com.sansec</groupId>
            <artifactId>seckms-crypto</artifactId>
            <version>1.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>

    </dependencies>

    <dependencyManagement>
<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>org.apache.pdfbox</groupId>-->
<!--                <artifactId>pdfbox</artifactId>-->
<!--                <version>2.0.27</version>-->
<!--            </dependency>-->
<!--        </dependencies>-->
    </dependencyManagement>
    <build>
        <finalName>${out.jarname}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>


            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <configuration>
                    <finalName>${out.jarname}</finalName>
                </configuration>
                <executions>  <!--执行器 mvn assembly:assembly-->
                    <execution>
                        <id>release</id><!--名字任意 -->
                        <phase>package</phase><!-- 绑定到package生命周期阶段上 -->
                        <goals>
                            <goal>single</goal><!-- 只运行一次 -->
                        </goals>
                        <configuration>
                            <descriptors> <!--描述文件路径-->
                                <descriptor>./assembly/assembly.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>