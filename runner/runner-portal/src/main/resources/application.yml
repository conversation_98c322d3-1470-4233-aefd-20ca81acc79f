server:
  port: 18082
  servlet:
    context-path: /
spring:
  profiles:
    active: dev
  application:
    name: ai-unified-portal
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  aop:
    proxy-target-class: true
  messages:
    basename: i18n.messages,i18n.common,i18n.base
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  datasource:
    initialSize: 30
    maxActive: 500
    minIdle: 30
    maxWait: 20000
    timeBetweenConnectErrorMillis: 90000
    timeBetweenEvictionRunsMillis: 60000
    validationQuery: SELECT 1
    testOnBorrow: false
    testOnReturn: false
    testWhileIdle: true
    druid:
      validation-query: SELECT 1
  servlet:
    multipart:
      max-file-size: 1500MB
      max-request-size: 1500MB
  jmx:
    enabled: false  #禁用远程调试

mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

key:
  lmk1:
    value: +Rvtz3+unEEL0RfTtEsDBQ==
  lmk2:
    path: config/lmk2
environment:
  systemPropertyListKey:
    - key.lmk1.value
    - key.lmk2.path
  decryptionListKey:
    - spring.datasource.username
    - spring.datasource.password
    - secplat.base.hmac
    - secplat.base.token
    - secplat.base.packagepub
    - secplat.base.packagepri
    - ai.secret.private-key
    - spring.ldap.username
    - spring.ldap.password

sec:
  log:
    logPath: config/log.ini

#web平台配置
secplat:
  base:
    hmac: 6hckh7UajoXDrzq/gT1CN7GQo6bQeb5jGqtdv9S41uw=
    token: 6QzvgguzK/y7rxRmEweFig==
    packagepub: je6dKhepncQffw5aguzUX77gKA3QiiAM/6A9dtFfAHy9dgcS6dlJ1hnp4VO3Wl8Kmu4A2XRXQZafIjg7Y9/n6C9WTWh+RfF1034vl0mgsip4r1iROF9sYTtkwz+D13O5
    packagepri: C3vi+6sK8eskfeWgwklq0gWCgTz/jsCnOx0++U7nTLwriXgVRVSDAD4BKpN9LhHU
    sysmode: 0
  restUrl: http://127.0.0.1:19442/sansecplat/

ai:
  secret:
    private-key: C/jDdLqqNv7ULkqRiszvCHtRTyr0f4fdibLJrfj//zJljKJIsrIkdR6dz06H/aVt
  cache:
    user:
      tokenExpireMinute: 30
      captchaExpireMinute: 5
  user:
    login:
      enableLock: 1
      lockCnt: 5
      lockTime: 60
      imageCodeCnt: 3
  portal:
    #统一门户地址
    url: ${ai.gateway-url}/portal/%s/
    #重定向地址，%s=portal_code
    redirect-url: ${ai.gateway-url}/portal/%s
    #前端门户cookie
    cookie-name: portal_code
  dify:
    #Dify实例管理端地址
    url: ${ai.gateway-url}/
    #重定向地址，%s=portal_code
    redirect-url: ${ai.gateway-url}/instance/%s
    #Dify实例部署根路径，该路径下包含：模板目录、所有Dify实例目录
    root_dir: /opt/sansec/dify
    #宿主机IP
    instance_ip: 127.0.0.1
    database_port: 5432
