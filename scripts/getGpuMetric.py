import subprocess
import re
import json

def runCommand(command):
    # 使用 subprocess.run 来执行命令并捕获输出
    result = subprocess.run(command, shell=True, text=True, capture_output=True)
    # 获取命令的标准输出
    output = result.stdout
    return output

def npu():
    npu_info = runCommand("""npu-smi info""").strip()
    # 使用正则表达式匹配所有非+-=|符号的字符
    pattern = r"[^+=|\-/]"  # [^+=|\-] 表示匹配除了 +、=、| 和 - 之外的所有字符
    # 使用 re.findall 找到所有匹配的字符
    result = re.findall(pattern, npu_info)
    data = "".join(result).split()
    result_map['toolType'] = "npu-smi"
    result_map['toolVersion'] = data[3]

    i = 15
    while i < len(data) and data[i] != 'NPU':
        data_map = data_map_template.copy()
        data_map["id"] = data[i]
        data_map["name"] = data[i + 1]
        data_map["power"] = data[i + 3]
        data_map["temp"] = data[i + 4]
        data_map["usageRate"] = int(data[i + 9])
        data_map["memUsage"] = int(data[i + 12])
        data_map["memTotal"] = int(data[i + 13])
        data_map["memUsageRate"] = data_map["memUsage"] / data_map["memTotal"] * 100

        result_map['memTotal'] += data_map["memTotal"]
        result_map['usageRate'] += data_map["usageRate"]
        result_map['memUsageRate'] += data_map["memUsageRate"]
        result_map["dataList"].append(data_map)

        i += 14

    result_map['count'] = len(result_map['dataList'])
    result_map['memTotal'] /= 1024
    result_map['usageRate'] /= result_map['count']
    result_map['memUsageRate'] /= result_map['count']
    json_output = json.dumps(result_map, indent=None)
    print(json_output)

def gpu():
    gpu_info = runCommand("nvidia-smi --query-gpu=index,name,power.draw,temperature.gpu,utilization.gpu,memory.used,memory.total,count,driver_version --format=csv,noheader,nounits").strip()
    rows = gpu_info.split("\n")
    result_map['toolType'] = "NVIDIA-SMI"
    for row in rows:
        data = row.split(",")
        result_map['count'] = data[7].strip()
        result_map['toolVersion'] = data[8].strip()
        data_map = data_map_template.copy()
        data_map["id"] = data[0].strip()
        data_map["name"] = data[1].strip()
        data_map["power"] = data[2].strip()
        data_map["temp"] = data[3].strip()
        data_map["usageRate"] = int(data[4].strip())
        data_map["memUsage"] = int(data[5].strip())
        data_map["memTotal"] = int(data[6].strip())
        data_map["memUsageRate"] = data_map["memUsage"] / data_map["memTotal"] * 100

        result_map['memTotal'] += data_map["memTotal"]
        result_map['usageRate'] += data_map["usageRate"]
        result_map['memUsageRate'] += data_map["memUsageRate"]
        result_map['dataList'].append(data_map)
    result_map['memTotal'] /= 1024
    result_map['usageRate'] /= len(rows)
    result_map['memUsageRate'] /= len(rows)
    json_output = json.dumps(result_map, indent=None)
    print(json_output)

if __name__ == '__main__':
    result_map = {
        "toolType":"",
        "toolVersion":"",
        "count":"",
        "memTotal":0,
        "usageRate":0,
        "memUsageRate":0,
        "dataList":[]
    }
    data_map_template = {
        "id":"",
        "name":"",
        "power":"",
        "temp":"",
        "usageRate":0,
        "memUsageRate": 0,
        "memUsage": 0,
        "memTotal":0
    }

    try:
        # 华为昇腾NPU
        subprocess.run(['npu-smi', '--help'], check=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        npu()
    except Exception:
        # 英伟达GPU
        gpu()



