#!/bin/bash

BASE_PATH="$( cd "$( dirname "${BASH_SOURCE[0]}" )/../" && pwd )"
SCRIPT_PATH="${BASE_PATH}/scripts"
DATA_PATH="${BASE_PATH}/data"
LOG_PATH=${BASE_PATH}"/logs"
CONFIG_PATH="${BASE_PATH}/config"
BIN_PATH="${BASE_PATH}/bin"
TOOLS_PATH="${BASE_PATH}/tools"

# 测试阶段启动时需要记录nohup日志，则配置具体文件名称。
# 如果不配置，则不生成nohup日志。
# NOHUP_LOG_FILE=
NOHUP_LOG_FILE=server-nohup.log

# 需要与打包生成的jar包名称相同
APP=crypto-ai-one-system.jar

# 部署到服务器上后，需要修改此处，填写服务器的JDK的绝对路径
JAVA_HOME=/opt/sansec/openjdk17/bin/java

JAVA_OPT="-XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPT="$JAVA_OPT  -XX:HeapDumpPath=$DATA_PATH"
JAVA_OPT="$JAVA_OPT  -XX:NativeMemoryTracking=detail"
JAVA_OPT="$JAVA_OPT  -Xmx1024m -Xms1024m"

cd $BASE_PATH

stop(){
    ID=`ps -ef | grep $1 | grep -v "grep" | awk '{print $2}'`
    for id in $ID
    do
        r=`kill -9 $id 2>&1`
    done
}

start(){
  if [[ "$NOHUP_LOG_FILE" == "" ]]; then
    nohup  $JAVA_HOME -jar $JAVA_OPT $BIN_PATH/$APP > /dev/null 2>&1 &
  else
    nohup  $JAVA_HOME -jar $JAVA_OPT $BIN_PATH/$APP > $LOG_PATH/$NOHUP_LOG_FILE 2>&1 &
  fi
}

status(){
   NUM=`ps -ef | grep $APP | grep -v "grep" | wc -l`
   if [ $NUM -eq 1 ]
   then
      echo "Running"
   else
      echo "Stoped"
   fi
}

if [[ $1 == "start" ]];then
    start
elif [[ $1 == "stop" ]];then
    stop $APP
elif [[ $1 == "restart" ]];then
    stop $APP
    sleep 2
    start
elif [[ $1 == "status" ]];then
    status
else
    echo "./start.sh start|stop|restart|status"
fi

