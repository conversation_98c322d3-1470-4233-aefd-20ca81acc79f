package com.sansec.ai.rag;


import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/rag")
@RequiredArgsConstructor
public class RagController {

    private final RagService ragService;

    @GetMapping("/importDocument")
    public void importDocument() {
        ragService.importDocuments();
    }

    @GetMapping("/rag")
    public ChatResponse generate(@RequestParam(value = "message",
            defaultValue = "how to get start with spring ai alibaba?") String message) {
        return ragService.retrieve(message);
    }

}
