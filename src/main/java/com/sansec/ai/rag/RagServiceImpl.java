package com.sansec.ai.rag;

import lombok.RequiredArgsConstructor;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.PromptTemplate;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.template.st.StTemplateRenderer;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

@Service
@RequiredArgsConstructor
public class RagServiceImpl implements RagService{

    private static final String textField = "content";

    private static final String vectorField = "embedding";

    private final ChatModel chatModel;
    private final VectorStore vectorStore;

    @Value("classpath:/prompts/system-qa.st")
    private Resource systemResource;

    @Override
    public void importDocuments() {

        // 1. parse document
        String pdfPath = "E:\\AI_CODE\\spring_ai_alibaba_quickstart.pdf";
        FileSystemResource resource = new FileSystemResource(pdfPath);
        PagePdfDocumentReader pagePdfDocumentReader = new PagePdfDocumentReader(resource);
        List<Document> documents = pagePdfDocumentReader.get();
        for (Document document : documents) {
            System.out.println(document);
        }


        // 2. split trunks
        List<Document> splitDocuments = new TokenTextSplitter(800, 350, 5, 10000, true).apply(documents);

        // 3. create embedding and store to vector store
//        createIndexIfNotExists();
        vectorStore.add(splitDocuments);
    }

    private void createIndexIfNotExists() {

    }

    @Override
    public ChatResponse retrieve(String message) {
        // Enable hybrid search, both embedding and full text search
        SearchRequest searchRequest = SearchRequest.builder().
                topK(4)
                .similarityThresholdAll()
                .filterExpression(new FilterExpressionBuilder().eq(textField, message).build())
                .build();

        // Step3 - Retrieve and llm generate
        String promptTemplate = getPromptTemplate(systemResource);

        PromptTemplate customPromptTemplate = PromptTemplate.builder()
                .renderer(StTemplateRenderer.builder().startDelimiterToken('{').endDelimiterToken('}').build())
                .template(promptTemplate)
                .build();


        QuestionAnswerAdvisor qaAdvisor = QuestionAnswerAdvisor.builder(vectorStore)
                .promptTemplate(customPromptTemplate)
                .build();

//        Flux<ServerSentEvent<String>> map = ChatClient.builder(chatModel)
//                .build().prompt()
//                .advisors(qaAdvisor)
//                .user(message)
//                .stream()
//                .content()
//                .map(content -> ServerSentEvent.<String>builder()
//                        .data(content)
//                        .build());

        ChatResponse chatResponse = ChatClient.builder(chatModel)
                .build().prompt()
                .advisors(qaAdvisor)
                .user(message)
                .call()
                .chatResponse();


        return chatResponse;
    }

    private String getPromptTemplate(Resource systemResource) {
        try {
            return systemResource.getContentAsString(StandardCharsets.UTF_8);
        }
        catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

}
