package com.sansec.ai.wrokflow_2;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.node.LlmNode;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;

@Component
public class GraphBuilder_2 {

    @Bean
    public CompiledGraph buildGraph(ChatModel chatModel) throws Exception {
        ChatClient chatClient = ChatClient.builder(chatModel).defaultAdvisors(new SimpleLoggerAdvisor()).build();

        // new stateGraph
        // todo: Add some non-node variable names, such as sys and conversation variables. Format as sys_xxx.
        StateGraph stateGraph = new StateGraph(() -> {
            Map<String, KeyStrategy> strategies = new HashMap<>();
            strategies.put("startNode1_result", (o1, o2) -> o2);
            strategies.put("end1_output", (o1, o2) -> o2);
            strategies.put("LLMNode1_text", (o1, o2) -> o2);
            return strategies;
        });
        // add nodes
        // EndNode [ 1756446741620 ]
        stateGraph.addNode("end1", AsyncNodeAction
                .node_async(state -> Map.of("end1_output", Map.of("result", state.value("LLMNode1_text").orElse("")))));// —— LlmNode [1756449387223] ——
        LlmNode LLMNode1 = LlmNode.builder().systemPromptTemplate("你将扮演一个角色，如果用户问你的名字，则直接输出你扮演角色的名字")
                .userPromptTemplate("你扮演的角色名字是{startNode1_result}\n/no_think")
                .params(Map.of("startNode1_result", "null")).chatClient(chatClient).outputKey("LLMNode1_text").build();
        stateGraph.addNode("LLMNode1", AsyncNodeAction.node_async(state -> {
            Map<String, Object> result = LLMNode1.apply(state);
            String key = "LLMNode1_text";
            Object object = result.get(key);
            if (object instanceof AssistantMessage && ((AssistantMessage) object).getText() != null) {
                return Map.of(key, ((AssistantMessage) object).getText());
            }
            return Map.of(key, object != null ? object.toString() : "unknown");
        }));

        // add edges
        stateGraph.addEdge(START, "LLMNode1");
        stateGraph.addEdge("LLMNode1", "end1");
        stateGraph.addEdge("end1", END);

        return stateGraph.compile();
    }

}
