package com.sansec.ai.wrokflow_1;

import com.alibaba.cloud.ai.graph.CompiledGraph;
import com.alibaba.cloud.ai.graph.KeyStrategy;
import com.alibaba.cloud.ai.graph.StateGraph;
import com.alibaba.cloud.ai.graph.action.AsyncNodeAction;
import com.alibaba.cloud.ai.graph.node.code.CodeExecutor;
import com.alibaba.cloud.ai.graph.node.code.CodeExecutorNodeAction;
import com.alibaba.cloud.ai.graph.node.code.LocalCommandlineCodeExecutor;
import com.alibaba.cloud.ai.graph.node.code.entity.CodeExecutionConfig;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.SimpleLoggerAdvisor;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.cloud.ai.graph.StateGraph.END;
import static com.alibaba.cloud.ai.graph.StateGraph.START;

@Component
public class GraphBuilder {

    @Bean
    public CompiledGraph buildGraph(ChatModel chatModel, CodeExecutionConfig codeExecutionConfig, CodeExecutor codeExecutor) throws Exception {
        ChatClient chatClient = ChatClient.builder(chatModel).defaultAdvisors(new SimpleLoggerAdvisor()).build();

        // new stateGraph
        // todo: Add some non-node variable names, such as sys and conversation variables. Format as sys_xxx.
        StateGraph stateGraph = new StateGraph(() -> {
            Map<String, KeyStrategy> strategies = new HashMap<>();
            strategies.put("startNode1_result", (o1, o2) -> o2);
            strategies.put("end1_output", (o1, o2) -> o2);
            strategies.put("codeNode1_result", (o1, o2) -> o2);
            return strategies;
        });
        // add nodes
        // EndNode [ 1756446741620 ]
        stateGraph.addNode("end1", AsyncNodeAction.node_async(
                state -> Map.of("end1_output", Map.of("result", state.value("codeNode1_result").orElse("")))));// —— CodeNode [1756446771338] ——
        CodeExecutorNodeAction codeNode1 = CodeExecutorNodeAction.builder().codeExecutor(codeExecutor) // 注入的 CodeExecutor Bean
                .codeLanguage("python").code("""

                        def main(arg1) -> dict:
                            return {
                                "result": str(arg1 + 1),
                            }

                        """).config(codeExecutionConfig) // 注入的 CodeExecutionConfig Bean
                .params(Map.of("arg1", "startNode1_result")).outputKey("codeNode1_output").build();
        stateGraph.addNode("codeNode1", AsyncNodeAction.node_async((state) -> {
            // 将代码运行的结果拆包
            Map<String, Object> result = codeNode1.apply(state);
            String key = "codeNode1_output";
            Object object = result.get(key);
            if (!(object instanceof Map)) {
                return Map.of();
            }
            return ((Map<String, Object>) object).entrySet().stream()
                    .collect(Collectors.toMap(entry -> "codeNode1_" + entry.getKey(), Map.Entry::getValue));
        }));

        // add edges
        stateGraph.addEdge(START, "codeNode1");
        stateGraph.addEdge("codeNode1", "end1");
        stateGraph.addEdge("end1", END);

        return stateGraph.compile();
    }

    @Bean
    public Path tempDir() throws IOException {
        // todo： set your work dir
        Path tempDir = Files.createTempDirectory("code-execution-workdir-");
        tempDir.toFile().deleteOnExit();
        return tempDir;
    }

    @Bean
    public CodeExecutionConfig codeExecutionConfig(Path tempDir) {
        return new CodeExecutionConfig().setWorkDir(tempDir.toString());
    }

    @Bean
    public CodeExecutor codeGenerator() {
        return new LocalCommandlineCodeExecutor();
    }
}
