server:
  port: 18080
  servlet:
    context-path: /ai/ops
spring:
  main:
    allow-bean-definition-overriding: true
  application:
    name: crypto-ai-one-system
  messages:
    basename: i18n.messages,i18n.common,i18n.base
  cloud:
    loadbalancer:
      retry:
        enabled: true

  ai:
    openai:
      base-url: https://ai.secsign.online:3003
      chat:
        options:
          model: qwen3-32b
      api-key: sk-hBB0Icl2AlA58nOuHydvHRX33Cagdl3etG04k0AI4F4fzDvG
    vectorstore:
      weaviate:
        api-key: WVF5YThaHlkYwhGUSmCRgsX3tD5ngdN8pkih
        host: ************:8007
        scheme: http
        object-class: SpringAI

sec:
  log:
    logPath: config/log.ini
sansec:
  ai:
    common:
      #root-path: /opt/sansec/crypto-ai-one-system/config/
      #file-suffix: .sh
      #api-key-path: /opt/sansec/secuLlama/api_keys
      root-path: D:\4-javaProject\ai-club\crypto-ai-one-system\config\
      file-suffix: .bat
      api-key-path: D:\var\ollama\api_keys
      api-key-enc-index:
      api-key-enc-key: 1234567812345678
      token-key: 6QzvgguzK/y7rxRmEweFig==
      root-ca-cert: MIICOzCCAd6gAwIBAgIIbAbZnrBMKUgwDAYIKoEcz1UBg3UFADCBhTELMAkGA1UEBhMCQ04xDzANBgNVBAgMBuWMl+S6rDEPMA0GA1UEBwwG5YyX5LqsMRUwEwYDVQQKDAzkuInmnKrkv6HlrokxFzAVBgNVBAMMDlNBTlNFQyBQRCBST09UMSQwIgYJKoZIhvcNAQkBFhVzdXBwb3J0QHNhbnNlYy5jb20uY24wIBcNMTkwOTA0MDY0NzE0WhgPMjA2OTA4MjMwNjQ3MTRaMIGFMQswCQYDVQQGEwJDTjEPMA0GA1UECAwG5YyX5LqsMQ8wDQYDVQQHDAbljJfkuqwxFTATBgNVBAoMDOS4ieacquS/oeWuiTEXMBUGA1UEAwwOU0FOU0VDIFBEIFJPT1QxJDAiBgkqhkiG9w0BCQEWFXN1cHBvcnRAc2Fuc2VjLmNvbS5jbjBZMBMGByqGSM49AgEGCCqBHM9VAYItA0IABPKozG3hSRCGFUng9TfmOWcLQNiBrq4a3D4q8SM6cDQVslluFg95jjhDdq+8pshVtk/2KfyzX3VW6EuZdFxDduOjMjAwMA8GA1UdEwEB/wQFMAMBAf8wHQYDVR0OBBYEFELufEtM6TYgi+igpD6H062y1dOKMAwGCCqBHM9VAYN1BQADSQAwRgIhAJVvgnYYtxib6Og51spY0EcWmEZttRh2e+l207ygPicyAiEAu8a2kdv0H5YjWFAQtp7WI4inQdpLpssBJu0w19iVaLQ=
      config-file-path: config/loginConfig.ini
      login-username: admin
      login-psd: admin@1234.
      create-new-user: false
      encrypt-psd: true
      psd-verify-index: 1
      swsds-path: config/swsds.ini
      token-cache-minute: 30
      serve-default-config-path: config/serveDefaultConfig.json
      serve-new-config-path: config/serveConfig.json
      serve-start-script-path: ${sansec.ai.common.root-path}serve_start${sansec.ai.common.file-suffix}
      serve-stop-script-path: ${sansec.ai.common.root-path}serve_stop${sansec.ai.common.file-suffix}
      serve-status-script-path: ${sansec.ai.common.root-path}serve_status_check${sansec.ai.common.file-suffix}
      serve-version-get-script-path: ${sansec.ai.common.root-path}serve_version${sansec.ai.common.file-suffix}
      serve-url-format: "%s://127.0.0.1:%s"

monitor:
  port: 19441

